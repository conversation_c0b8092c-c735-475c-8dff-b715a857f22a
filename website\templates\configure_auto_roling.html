{% extends "base.html" %}

{% block title %}Auto-Roling Configuration - {{ server_info.name }}{% endblock %}

{% block styles %}
<style>
    .config-header {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-color);
    }

    .config-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6, #6366f1);
    }

    .config-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .config-subtitle {
        color: var(--text-secondary);
        font-size: 1.1rem;
        margin-bottom: 0;
        opacity: 0.9;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        backdrop-filter: blur(20px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Premium Feature Styles */
    .premium-feature {
        position: relative;
        opacity: 0.6;
        pointer-events: none;
    }

    .premium-feature::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.3);
        border-radius: var(--border-radius);
        z-index: 1;
    }

    .premium-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 2;
        text-align: center;
        color: white;
        font-weight: 600;
    }

    .premium-overlay .fas {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: #fbbf24;
    }

    .premium-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background: linear-gradient(135deg, #fbbf24, #f59e0b);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
        z-index: 3;
    }

    .feature-tier {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
        margin-left: 0.5rem;
    }

    .tier-free {
        background: rgba(34, 197, 94, 0.2);
        color: #22c55e;
        border: 1px solid rgba(34, 197, 94, 0.3);
    }

    .tier-premium {
        background: rgba(251, 191, 36, 0.2);
        color: #fbbf24;
        border: 1px solid rgba(251, 191, 36, 0.3);
    }

    .premium-cta {
        background: linear-gradient(135deg, #fbbf24, #f59e0b);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        margin-top: 1rem;
    }

    .premium-cta:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(251, 191, 36, 0.4);
        color: white;
        text-decoration: none;
    }
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        border-color: #3b82f6;
        box-shadow: 0 15px 40px rgba(59, 130, 246, 0.15);
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6, #6366f1);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .feature-card:hover::before {
        opacity: 1;
    }
</style>
{% endblock %}

{% block content %}
<!-- Include Premium Features JavaScript -->
<script src="{{ url_for('static', filename='js/premium-features.js') }}"></script>

<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        {% include 'sidebar.html' %}
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <!-- Configuration Header -->
        <div class="config-header">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h1 class="config-title">
                        <i class="fas fa-user-plus text-primary"></i>
                        Auto-Roling Configuration
                    </h1>
                    <p class="config-subtitle">Basic role assignment free • Advanced features require premium</p>
                </div>
                <div class="d-flex gap-2">
                    {% if not has_premium %}
                    <a href="/shop" class="premium-cta">
                        <i class="fas fa-crown me-2"></i>Upgrade to Premium
                    </a>
                    {% endif %}
                    <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Configuration Sections -->
        <div class="row">
            <!-- Free Features -->
            <div class="col-lg-6 mb-4">
                <div class="feature-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>
                            Basic Auto-Role
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="basicAutoRoleForm">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <input type="hidden" name="action" value="configure_basic">

                            <div class="mb-3">
                                <label for="role_id" class="form-label">Auto-Role</label>
                                <select class="form-select" id="role_id" name="role_id">
                                    <option value="">Select a role...</option>
                                    <!-- Roles will be populated by JavaScript -->
                                </select>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    This role will be automatically assigned to new members.
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enabled" name="enabled"
                                           {% if auto_roling_settings and auto_roling_settings.enabled %}checked{% endif %}>
                                    <label class="form-check-label" for="enabled">
                                        Enable Auto-Roling
                                    </label>
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Basic Configuration
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Premium Features -->
            <div class="col-lg-6 mb-4">
                <!-- Multiple Join Roles -->
                <div class="feature-card mb-3 {% if not has_premium %}premium-feature{% endif %}">
                    {% if not has_premium %}
                    <div class="premium-badge">PREMIUM</div>
                    <div class="premium-overlay">
                        <i class="fas fa-crown"></i>
                        <div>This is a premium feature</div>
                        <a href="/shop" class="btn btn-sm btn-warning mt-2">Purchase Premium</a>
                    </div>
                    {% endif %}
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            Multiple Join Roles
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if has_premium %}
                        <form method="POST" id="multipleRolesForm">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <input type="hidden" name="action" value="configure_multiple_roles">

                            <!-- Hidden element to store existing additional roles data -->
                            <script type="application/json" id="existingAdditionalRoles">
                                {% if auto_roling_settings and auto_roling_settings.additional_join_roles %}
                                [{% for role_id in auto_roling_settings.additional_join_roles %}"{{ role_id }}"{% if not loop.last %},{% endif %}{% endfor %}]
                                {% else %}
                                []
                                {% endif %}
                            </script>

                            <div class="mb-3">
                                <label class="form-label">Additional Join Roles (up to 5 total)</label>
                                <div id="multipleRolesContainer">
                                    <!-- Multiple role selectors will be added here -->
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="addRoleBtn">
                                    <i class="fas fa-plus me-1"></i>Add Role
                                </button>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>Save Multiple Roles
                                </button>
                            </div>
                        </form>
                        {% else %}
                        <p class="text-muted">Assign up to 5 different roles to new members when they join.</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Offline Protection -->
                <div class="feature-card mb-3 {% if not has_premium %}premium-feature{% endif %}">
                    {% if not has_premium %}
                    <div class="premium-badge">PREMIUM</div>
                    <div class="premium-overlay">
                        <i class="fas fa-crown"></i>
                        <div>This is a premium feature</div>
                        <a href="/shop" class="btn btn-sm btn-warning mt-2">Purchase Premium</a>
                    </div>
                    {% endif %}
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            Offline Protection
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if has_premium %}
                        <form method="POST" id="offlineProtectionForm">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <input type="hidden" name="action" value="configure_offline_protection">

                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="offline_protection" name="offline_protection"
                                           {% if auto_roling_settings and auto_roling_settings.get('offline_protection_enabled') %}checked{% endif %}>
                                    <label class="form-check-label" for="offline_protection">
                                        Enable Offline Protection
                                    </label>
                                </div>
                                <div class="form-text">
                                    Automatically assign roles to members who joined while the bot was offline.
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>Save Offline Protection
                                </button>
                            </div>
                        </form>
                        {% else %}
                        <p class="text-muted">Automatically assign roles to members who joined while the bot was offline.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Premium Features -->
        <div class="row">
            <!-- Reaction Roles -->
            <div class="col-lg-6 mb-4">
                <div class="feature-card {% if not has_premium %}premium-feature{% endif %}">
                    {% if not has_premium %}
                    <div class="premium-badge">PREMIUM</div>
                    <div class="premium-overlay">
                        <i class="fas fa-crown"></i>
                        <div>This is a premium feature</div>
                        <a href="/shop" class="btn btn-sm btn-warning mt-2">Purchase Premium</a>
                    </div>
                    {% endif %}
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-smile me-2"></i>
                            Reaction Roles
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if has_premium %}
                        <p class="text-muted mb-3">Let users assign themselves roles by reacting to messages.</p>

                        <div class="mb-3">
                            <label class="form-label">Reaction Role Messages</label>
                            <div id="reactionRolesList">
                                <!-- Existing reaction role messages will be listed here -->
                                <div class="text-muted text-center py-3">
                                    <i class="fas fa-smile fa-2x mb-2"></i>
                                    <p>No reaction role messages created yet.</p>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-primary" id="createReactionRoleBtn" onclick="openReactionRoleModal()">
                                <i class="fas fa-plus me-2"></i>Create Reaction Role Message
                            </button>
                            <small class="text-muted">Up to 20 roles per message, 10 messages max</small>
                        </div>
                        {% else %}
                        <p class="text-muted">Let users assign themselves roles by reacting to messages with emojis.</p>
                        <ul class="list-unstyled text-muted">
                            <li><i class="fas fa-star text-warning me-2"></i>Up to 20 roles per message</li>
                            <li><i class="fas fa-star text-warning me-2"></i>Up to 10 reaction messages</li>
                            <li><i class="fas fa-star text-warning me-2"></i>Custom emojis supported</li>
                        </ul>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Embed Dropdown Roles -->
            <div class="col-lg-6 mb-4">
                <div class="feature-card {% if not has_premium %}premium-feature{% endif %}">
                    {% if not has_premium %}
                    <div class="premium-badge">PREMIUM</div>
                    <div class="premium-overlay">
                        <i class="fas fa-crown"></i>
                        <div>This is a premium feature</div>
                        <a href="/shop" class="btn btn-sm btn-warning mt-2">Purchase Premium</a>
                    </div>
                    {% endif %}
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            Embed Dropdown Roles
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if has_premium %}
                        <p class="text-muted mb-3">Create beautiful embed messages with dropdown role selection.</p>

                        <div class="mb-3">
                            <label class="form-label">Embed Dropdown Messages</label>
                            <div id="embedDropdownsList">
                                <!-- Existing embed dropdown messages will be listed here -->
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-primary" id="createEmbedDropdownBtn">
                                <i class="fas fa-plus me-2"></i>Create Embed Dropdown
                            </button>
                            <small class="text-muted">Up to 3 embeds, 25 roles per dropdown</small>
                        </div>
                        {% else %}
                        <p class="text-muted">Create beautiful embed messages with dropdown menus for role selection.</p>
                        <ul class="list-unstyled text-muted">
                            <li><i class="fas fa-star text-warning me-2"></i>Custom embed design</li>
                            <li><i class="fas fa-star text-warning me-2"></i>Up to 25 roles per dropdown</li>
                            <li><i class="fas fa-star text-warning me-2"></i>Up to 3 embed messages</li>
                            <li><i class="fas fa-star text-warning me-2"></i>Live preview</li>
                        </ul>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reaction Role Creation Modal -->
<div class="modal fade" id="reactionRoleModal" tabindex="-1" aria-labelledby="reactionRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reactionRoleModalLabel">
                    <i class="fas fa-smile me-2"></i>Create Reaction Role Message
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="reactionRoleForm">
                    <div class="mb-3">
                        <label for="reactionRoleChannel" class="form-label">Channel</label>
                        <select class="form-select" id="reactionRoleChannel" required>
                            <option value="">Select a channel...</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="reactionRoleTitle" class="form-label">Message Title</label>
                        <input type="text" class="form-control" id="reactionRoleTitle" placeholder="React to get roles!" maxlength="100">
                    </div>

                    <div class="mb-3">
                        <label for="reactionRoleDescription" class="form-label">Message Description</label>
                        <textarea class="form-control" id="reactionRoleDescription" rows="3" placeholder="React with the emojis below to get your roles!" maxlength="500"></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Role-Emoji Pairs</label>
                        <div id="roleEmojiPairs">
                            <!-- Role-emoji pairs will be added here -->
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="addRoleEmojiPair">
                            <i class="fas fa-plus me-1"></i>Add Role-Emoji Pair
                        </button>
                        <small class="text-muted d-block mt-1">Maximum 20 role-emoji pairs per message</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="createReactionRoleMessage">
                    <i class="fas fa-paper-plane me-2"></i>Create Message
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialize premium features for auto-roling page
document.addEventListener('DOMContentLoaded', async function() {
    // Define feature mappings for this page
    const featureMap = {
        '.premium-feature[data-feature="auto_roling"][data-subfeature="multiple_roles"]': {
            feature: 'auto_roling',
            subfeature: 'multiple_roles',
            action: 'badge'
        },
        '.premium-feature[data-feature="auto_roling"][data-subfeature="advanced_triggers"]': {
            feature: 'auto_roling',
            subfeature: 'advanced_triggers',
            action: 'badge'
        },
        '.premium-feature[data-feature="auto_roling"][data-subfeature="role_hierarchy"]': {
            feature: 'auto_roling',
            subfeature: 'role_hierarchy',
            action: 'badge'
        }
    };

    // Initialize premium features
    await window.premiumFeatures.initializeContainer('#premium-features-card', featureMap);

    // Add click handlers for premium feature buttons
    document.querySelectorAll('.premium-feature-btn').forEach(btn => {
        btn.addEventListener('click', async function(e) {
            e.preventDefault();

            const featureDiv = this.closest('.premium-feature');
            const feature = featureDiv.dataset.feature;
            const subfeature = featureDiv.dataset.subfeature;

            const hasAccess = await window.premiumFeatures.handleFeatureClick(
                feature,
                subfeature,
                () => {
                    // This would be the actual feature implementation
                    alert(`${subfeature.replace('_', ' ')} feature would be configured here!`);
                }
            );

            if (hasAccess) {
                this.disabled = false;
                this.classList.remove('btn-outline-primary');
                this.classList.add('btn-primary');
            }
        });
    });
});

let serverRoles = [];

// Load Discord data for dropdowns
document.addEventListener('DOMContentLoaded', function() {
    loadServerRoles();
});

async function loadServerRoles() {
    try {
        const response = await fetch('/api/roles');
        const data = await response.json();

        console.log('Roles API response:', data);

        if (Array.isArray(data)) {
            serverRoles = data;
            console.log('Loaded server roles:', serverRoles.length);
            populateRoleDropdown();
            // Load existing additional roles after server roles are loaded
            setTimeout(() => {
                loadExistingAdditionalRoles();
            }, 200); // Give a bit more time for DOM to be ready
        } else if (data.error) {
            console.error('Error from roles API:', data.error);
        }
    } catch (error) {
        console.error('Error loading server roles:', error);
    }
}

function populateRoleDropdown() {
    const roleSelect = document.getElementById('role_id');
    if (!roleSelect) {
        console.error('Role select element not found!');
        return;
    }

    console.log('Populating role dropdown...');
    console.log('Available roles:', serverRoles.length);

    // Clear existing options
    roleSelect.innerHTML = '<option value="">Select a role...</option>';

    // Filter to only show non-managed roles (the API already filters out @everyone)
    const assignableRoles = serverRoles.filter(role => !role.managed);

    console.log('Assignable roles:', assignableRoles.length);

    // Sort roles by position (highest first)
    assignableRoles.sort((a, b) => b.position - a.position);

    // Add all assignable roles to dropdown
    assignableRoles.forEach(role => {
        const option = document.createElement('option');
        option.value = role.id;
        option.textContent = role.name;

        // Set color if available
        if (role.color && role.color !== 0) {
            option.style.color = `#${role.color.toString(16).padStart(6, '0')}`;
        }

        // Select current role if editing
        {% if auto_roling_settings and auto_roling_settings.role_id %}
        if (role.id === '{{ auto_roling_settings.role_id }}') {
            option.selected = true;
        }
        {% endif %}

        roleSelect.appendChild(option);
    });
}

// Multiple Join Roles functionality
let additionalRoleCount = 0;

document.addEventListener('DOMContentLoaded', function() {
    // Add role button handler
    const addRoleBtn = document.getElementById('addRoleBtn');
    if (addRoleBtn) {
        addRoleBtn.addEventListener('click', addAdditionalRole);
    }

    // Debug: Check if elements exist
    console.log('DOM loaded, checking elements:');
    console.log('- addRoleBtn:', !!addRoleBtn);
    console.log('- multipleRolesContainer:', !!document.getElementById('multipleRolesContainer'));
    console.log('- existingAdditionalRoles:', !!document.getElementById('existingAdditionalRoles'));

    // Note: loadExistingAdditionalRoles() is now called after server roles are loaded
});

function addAdditionalRole() {
    if (additionalRoleCount >= 4) { // Max 5 total (1 basic + 4 additional)
        alert('Maximum of 5 total roles allowed (1 basic + 4 additional)');
        return;
    }

    const container = document.getElementById('multipleRolesContainer');
    const roleDiv = document.createElement('div');
    roleDiv.className = 'mb-2 d-flex gap-2';
    roleDiv.innerHTML = `
        <select class="form-select" name="additional_roles[]" required>
            <option value="">Select additional role...</option>
        </select>
        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeAdditionalRole(this)">
            <i class="fas fa-trash"></i>
        </button>
    `;

    container.appendChild(roleDiv);

    // Populate the new dropdown
    const newSelect = roleDiv.querySelector('select');

    console.log(`Adding new role dropdown, serverRoles length: ${serverRoles.length}`);

    if (serverRoles.length === 0) {
        console.warn('Server roles not loaded yet for new dropdown');
        newSelect.innerHTML = '<option value="">Loading roles...</option>';
        // Wait for roles to load
        const checkRoles = setInterval(() => {
            if (serverRoles.length > 0) {
                clearInterval(checkRoles);
                populateAdditionalRoleDropdown(newSelect);
            }
        }, 100);
    } else {
        populateAdditionalRoleDropdown(newSelect);
    }

    additionalRoleCount++;
    updateAddRoleButton();
}

function addAdditionalRoleWithValue(roleId) {
    if (additionalRoleCount >= 4) { // Max 5 total (1 basic + 4 additional)
        return;
    }

    const container = document.getElementById('multipleRolesContainer');
    const roleDiv = document.createElement('div');
    roleDiv.className = 'mb-2 d-flex gap-2';
    roleDiv.innerHTML = `
        <select class="form-select" name="additional_roles[]" required>
            <option value="">Select additional role...</option>
        </select>
        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeAdditionalRole(this)">
            <i class="fas fa-trash"></i>
        </button>
    `;

    container.appendChild(roleDiv);

    // Populate the new dropdown and set the value
    const newSelect = roleDiv.querySelector('select');

    console.log(`Populating dropdown for role ${roleId}, serverRoles length: ${serverRoles.length}`);

    if (serverRoles.length === 0) {
        console.warn('Server roles not loaded yet, waiting...');
        // If server roles aren't loaded yet, wait and try again
        const checkRoles = setInterval(() => {
            if (serverRoles.length > 0) {
                clearInterval(checkRoles);
                populateAdditionalRoleDropdown(newSelect);
                const roleIdString = String(roleId);
                newSelect.value = roleIdString;
                console.log(`Delayed set role dropdown value to: ${roleIdString}, actual value: ${newSelect.value}`);
            }
        }, 100);
        return;
    }

    populateAdditionalRoleDropdown(newSelect);

    // Set the value after a small delay to ensure options are populated
    setTimeout(() => {
        const roleIdString = String(roleId);

        // Try setting the value directly first
        newSelect.value = roleIdString;

        // If that didn't work, manually select the option
        if (newSelect.value !== roleIdString) {
            for (let option of newSelect.options) {
                if (option.value === roleIdString) {
                    option.selected = true;
                    break;
                }
            }
        }

        console.log(`Role ${roleId} set, dropdown shows: "${newSelect.value}"`);
    }, 300);

    additionalRoleCount++;
    updateAddRoleButton();
}

function removeAdditionalRole(button) {
    button.closest('.mb-2').remove();
    additionalRoleCount--;
    updateAddRoleButton();
}

function updateAddRoleButton() {
    const addBtn = document.getElementById('addRoleBtn');
    if (!addBtn) return;

    if (additionalRoleCount >= 4) {
        addBtn.disabled = true;
        addBtn.innerHTML = '<i class="fas fa-plus me-1"></i>Maximum Reached (5 total)';
    } else {
        addBtn.disabled = false;
        addBtn.innerHTML = '<i class="fas fa-plus me-1"></i>Add Role';
    }
}

function populateAdditionalRoleDropdown(selectElement) {
    if (!selectElement) {
        console.error('Select element is null');
        return;
    }

    if (!serverRoles || serverRoles.length === 0) {
        console.warn('Server roles not available for dropdown population');
        selectElement.innerHTML = '<option value="">Loading roles...</option>';
        return;
    }

    // Clear existing options
    selectElement.innerHTML = '<option value="">Select additional role...</option>';

    // Filter to only show non-managed roles
    const assignableRoles = serverRoles.filter(role => !role.managed);

    // Sort roles by position (highest first)
    assignableRoles.sort((a, b) => b.position - a.position);

    console.log(`Populating dropdown with ${assignableRoles.length} roles out of ${serverRoles.length} total`);

    // Add all assignable roles to dropdown
    assignableRoles.forEach(role => {
        const option = document.createElement('option');
        option.value = String(role.id); // Ensure role ID is a string
        option.textContent = role.name;

        // Set color if available
        if (role.color && role.color !== 0) {
            option.style.color = `#${role.color.toString(16).padStart(6, '0')}`;
        }

        selectElement.appendChild(option);
    });

    console.log(`Dropdown populated with ${selectElement.options.length} options:`,
        Array.from(selectElement.options).slice(0, 5).map(opt => ({value: opt.value, text: opt.text})));
}

function loadExistingAdditionalRoles() {
    // Load existing additional roles from the server data
    const existingRolesData = document.getElementById('existingAdditionalRoles');
    if (existingRolesData) {
        try {
            const existingRoles = JSON.parse(existingRolesData.textContent);
            console.log('Loading existing additional roles:', existingRoles);
            console.log('Server roles available:', serverRoles.length);

            if (existingRoles && existingRoles.length > 0) {
                existingRoles.forEach(roleId => {
                    if (roleId && additionalRoleCount < 4) {
                        console.log(`Adding existing role: ${roleId}`);
                        addAdditionalRoleWithValue(roleId);
                    }
                });
            } else {
                console.log('No existing additional roles to load');
            }
        } catch (e) {
            console.error('Error parsing existing additional roles:', e);
        }
    } else {
        console.log('No existingAdditionalRoles element found');
    }
}

// Form submission handlers
document.addEventListener('DOMContentLoaded', function() {
    console.log('Auto-roling configuration page loaded');

    // Basic auto-role form
    const basicForm = document.getElementById('basicAutoRoleForm');
    if (basicForm) {
        console.log('Basic auto-role form found');
        basicForm.addEventListener('submit', function(e) {
            console.log('Basic form submitted');
            const roleSelect = document.getElementById('role_id');
            if (!roleSelect.value) {
                e.preventDefault();
                alert('Please select a role for auto-roling');
                return false;
            }
            console.log('Basic form validation passed, submitting...');
        });
    } else {
        console.log('Basic auto-role form NOT found');
    }

    // Multiple roles form
    const multipleRolesForm = document.getElementById('multipleRolesForm');
    if (multipleRolesForm) {
        console.log('Multiple roles form found');
        multipleRolesForm.addEventListener('submit', function(e) {
            console.log('Multiple roles form submitted');
            const roleSelects = this.querySelectorAll('select[name="additional_roles[]"]');
            let hasValidRole = false;

            roleSelects.forEach(select => {
                if (select.value) {
                    hasValidRole = true;
                }
            });

            if (!hasValidRole) {
                e.preventDefault();
                alert('Please select at least one additional role');
                return false;
            }
            console.log('Multiple roles form validation passed, submitting...');
        });
    } else {
        console.log('Multiple roles form NOT found');
    }

    // Offline protection form
    const offlineForm = document.getElementById('offlineProtectionForm');
    if (offlineForm) {
        offlineForm.addEventListener('submit', function(e) {
            // Form is valid, no additional validation needed
        });
    }
});

// Reaction Roles functionality
let roleEmojiPairCount = 0;

function openReactionRoleModal() {
    // Load channels and roles
    loadChannelsForReactionRole();

    // Reset form
    document.getElementById('reactionRoleForm').reset();
    document.getElementById('roleEmojiPairs').innerHTML = '';
    roleEmojiPairCount = 0;

    // Add initial role-emoji pair
    addRoleEmojiPair();

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('reactionRoleModal'));
    modal.show();
}

async function loadChannelsForReactionRole() {
    try {
        const response = await fetch('/api/channels');
        const data = await response.json();

        const channelSelect = document.getElementById('reactionRoleChannel');
        channelSelect.innerHTML = '<option value="">Select a channel...</option>';

        if (Array.isArray(data)) {
            // Filter to text channels only
            const textChannels = data.filter(channel =>
                channel.type === 'text' || channel.type === 0 || channel.type === '0'
            );

            textChannels.forEach(channel => {
                const option = document.createElement('option');
                option.value = channel.id;
                option.textContent = `#${channel.name}`;
                channelSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading channels:', error);
    }
}

function addRoleEmojiPair() {
    if (roleEmojiPairCount >= 20) {
        alert('Maximum of 20 role-emoji pairs allowed per message');
        return;
    }

    const container = document.getElementById('roleEmojiPairs');
    const pairDiv = document.createElement('div');
    pairDiv.className = 'mb-2 p-3 border rounded';
    pairDiv.innerHTML = `
        <div class="row g-2">
            <div class="col-md-5">
                <label class="form-label">Role</label>
                <select class="form-select role-select" required>
                    <option value="">Select a role...</option>
                </select>
            </div>
            <div class="col-md-5">
                <label class="form-label">Emoji</label>
                <input type="text" class="form-control emoji-input" placeholder="😀 or :smile:" maxlength="50" required>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-outline-danger btn-sm w-100" onclick="removeRoleEmojiPair(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;

    container.appendChild(pairDiv);

    // Populate the role dropdown
    const roleSelect = pairDiv.querySelector('.role-select');
    populateRoleDropdown(roleSelect);

    roleEmojiPairCount++;
    updateAddPairButton();
}

function removeRoleEmojiPair(button) {
    button.closest('.mb-2').remove();
    roleEmojiPairCount--;
    updateAddPairButton();
}

function updateAddPairButton() {
    const addBtn = document.getElementById('addRoleEmojiPair');
    if (roleEmojiPairCount >= 20) {
        addBtn.disabled = true;
        addBtn.innerHTML = '<i class="fas fa-plus me-1"></i>Maximum Reached (20)';
    } else {
        addBtn.disabled = false;
        addBtn.innerHTML = '<i class="fas fa-plus me-1"></i>Add Role-Emoji Pair';
    }
}

function populateRoleDropdown(selectElement) {
    // Use the same serverRoles array from the main page
    if (!serverRoles || serverRoles.length === 0) {
        selectElement.innerHTML = '<option value="">Loading roles...</option>';
        return;
    }

    selectElement.innerHTML = '<option value="">Select a role...</option>';

    // Filter to only show non-managed roles
    const assignableRoles = serverRoles.filter(role => !role.managed);
    assignableRoles.sort((a, b) => b.position - a.position);

    assignableRoles.forEach(role => {
        const option = document.createElement('option');
        option.value = role.id;
        option.textContent = role.name;

        if (role.color && role.color !== 0) {
            option.style.color = `#${role.color.toString(16).padStart(6, '0')}`;
        }

        selectElement.appendChild(option);
    });
}

// Embed Dropdown functionality
function createEmbedDropdown() {
    // Placeholder for embed dropdown creation
    alert('Embed dropdown creation will be implemented here');
}

async function createReactionRoleMessage() {
    try {
        const channelId = document.getElementById('reactionRoleChannel').value;
        const title = document.getElementById('reactionRoleTitle').value;
        const description = document.getElementById('reactionRoleDescription').value;

        if (!channelId) {
            alert('Please select a channel');
            return;
        }

        // Collect role-emoji pairs
        const pairs = [];
        const pairElements = document.querySelectorAll('#roleEmojiPairs .mb-2');

        for (const pairElement of pairElements) {
            const roleSelect = pairElement.querySelector('.role-select');
            const emojiInput = pairElement.querySelector('.emoji-input');

            if (roleSelect.value && emojiInput.value) {
                pairs.push({
                    role_id: roleSelect.value,
                    emoji: emojiInput.value.trim()
                });
            }
        }

        if (pairs.length === 0) {
            alert('Please add at least one role-emoji pair');
            return;
        }

        // Create the reaction role message
        const response = await fetch('/configure/auto-roling', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                'csrf_token': document.querySelector('input[name="csrf_token"]').value,
                'action': 'create_reaction_role',
                'channel_id': channelId,
                'title': title,
                'description': description,
                'pairs': JSON.stringify(pairs)
            })
        });

        const result = await response.json();

        if (result.success) {
            alert('Reaction role message created successfully!');
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('reactionRoleModal'));
            modal.hide();
            // Reload the page to show the new message
            location.reload();
        } else {
            alert('Error: ' + (result.error || 'Failed to create reaction role message'));
        }

    } catch (error) {
        console.error('Error creating reaction role message:', error);
        alert('An error occurred while creating the reaction role message');
    }
}

async function loadExistingReactionRoles() {
    try {
        const response = await fetch('/api/reaction-roles');
        const data = await response.json();

        const container = document.getElementById('reactionRolesList');

        if (data.success && data.reaction_roles && data.reaction_roles.length > 0) {
            container.innerHTML = '';

            data.reaction_roles.forEach(reactionRole => {
                const roleDiv = document.createElement('div');
                roleDiv.className = 'card mb-3';
                roleDiv.innerHTML = `
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="card-title">${reactionRole.title || 'Reaction Role Message'}</h6>
                                <p class="card-text text-muted small">
                                    Channel: #${reactionRole.channel_name || 'Unknown'} |
                                    Roles: ${reactionRole.role_emoji_pairs ? reactionRole.role_emoji_pairs.length : 0}
                                </p>
                                <p class="card-text">${reactionRole.description || 'No description'}</p>
                            </div>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteReactionRole('${reactionRole.message_id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
                container.appendChild(roleDiv);
            });
        } else {
            container.innerHTML = `
                <div class="text-muted text-center py-3">
                    <i class="fas fa-smile fa-2x mb-2"></i>
                    <p>No reaction role messages created yet.</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading reaction roles:', error);
    }
}

async function deleteReactionRole(messageId) {
    if (!confirm('Are you sure you want to delete this reaction role message?')) {
        return;
    }

    try {
        const response = await fetch('/configure/auto-roling', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                'csrf_token': document.querySelector('input[name="csrf_token"]').value,
                'action': 'delete_reaction_role',
                'message_id': messageId
            })
        });

        const result = await response.json();

        if (result.success) {
            alert('Reaction role message deleted successfully!');
            loadExistingReactionRoles(); // Reload the list
        } else {
            alert('Error: ' + (result.error || 'Failed to delete reaction role message'));
        }
    } catch (error) {
        console.error('Error deleting reaction role:', error);
        alert('An error occurred while deleting the reaction role message');
    }
}

// Add event listeners for premium feature buttons
document.addEventListener('DOMContentLoaded', function() {
    const createReactionBtn = document.getElementById('createReactionRoleBtn');
    if (createReactionBtn) {
        createReactionBtn.addEventListener('click', openReactionRoleModal);
    }

    const createEmbedBtn = document.getElementById('createEmbedDropdownBtn');
    if (createEmbedBtn) {
        createEmbedBtn.addEventListener('click', createEmbedDropdown);
    }

    // Reaction role event listeners
    const addRoleEmojiBtn = document.getElementById('addRoleEmojiPair');
    if (addRoleEmojiBtn) {
        addRoleEmojiBtn.addEventListener('click', addRoleEmojiPair);
    }

    const createReactionRoleMessageBtn = document.getElementById('createReactionRoleMessage');
    if (createReactionRoleMessageBtn) {
        createReactionRoleMessageBtn.addEventListener('click', createReactionRoleMessage);
    }

    // Load existing reaction roles
    loadExistingReactionRoles();
});
</script>
{% endblock %}
