"""
Auto-Roling System Cog for Discord Bot
Handles automatic role assignment with freemium features:
- Basic auto-role (FREE): Single role assignment on join
- Multiple join roles (PREMIUM): Up to 5 roles on join
- Offline protection (PREMIUM): Background task to assign missed roles
- Reaction roles (PREMIUM): Role assignment via reactions
- Embed dropdown roles (PREMIUM): Role assignment via dropdown menus
"""

import discord
from discord.ext import commands, tasks
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union

logger = logging.getLogger(__name__)

class AutoRolingSystem(commands.Cog):
    """Comprehensive auto-roling system with freemium features"""

    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db

        # Rate limiting for role assignments
        self.role_assignment_cooldown = {}

    async def cog_load(self):
        """Called when the cog is loaded"""
        logger.info("Loading Auto-Roling System cog...")

        # Start background tasks
        if not self.offline_protection_task.is_running():
            self.offline_protection_task.start()

        # Load existing reaction roles and embed dropdowns
        await self.setup_existing_reaction_roles()
        await self.setup_existing_embed_dropdowns()

        logger.info("Auto-Roling System cog loaded successfully")

    async def cog_unload(self):
        """Called when the cog is unloaded"""
        logger.info("Unloading Auto-Roling System cog...")

        # Stop background tasks
        if self.offline_protection_task.is_running():
            self.offline_protection_task.cancel()

        logger.info("Auto-Roling System cog unloaded")

    async def setup_existing_reaction_roles(self):
        """Set up listeners for existing reaction role messages"""
        try:
            for guild in self.bot.guilds:
                reaction_roles = self.db.get_reaction_roles(guild.id)
                logger.info(f"Found {len(reaction_roles)} reaction role messages for guild {guild.name}")
        except Exception as e:
            logger.error(f"Error setting up existing reaction roles: {e}")

    async def setup_existing_embed_dropdowns(self):
        """Set up listeners for existing embed dropdown messages"""
        try:
            for guild in self.bot.guilds:
                embed_dropdowns = self.db.get_embed_dropdown_roles(guild.id)
                logger.info(f"Found {len(embed_dropdowns)} embed dropdown messages for guild {guild.name}")
        except Exception as e:
            logger.error(f"Error setting up existing embed dropdowns: {e}")

    # ========== PREMIUM CHECKING UTILITIES ==========

    def is_server_premium(self, guild_id: int) -> bool:
        """Check if a server has premium access"""
        try:
            # Get guild owner
            guild = self.bot.get_guild(guild_id)
            if not guild:
                return False

            owner_id = guild.owner_id
            return self.db.is_user_subscribed(owner_id)
        except Exception as e:
            logger.error(f"Error checking premium status for guild {guild_id}: {e}")
            return False

    def is_feature_enabled_for_server(self, guild_id: int, feature_name: str) -> bool:
        """Check if a specific feature is enabled for a server"""
        try:
            # Check admin disable flags
            config = self.db.get_server_config(guild_id)
            if config and config.get(f'{feature_name}_enabled') is False:
                return False

            # Check if auto-roling is globally disabled
            if config and config.get('auto_roling_enabled') is False:
                return False

            return True
        except Exception as e:
            logger.error(f"Error checking if {feature_name} is enabled for guild {guild_id}: {e}")
            return False

    # ========== MEMBER JOIN HANDLER ==========

    @commands.Cog.listener()
    async def on_member_join(self, member):
        """Handle member joins for auto-roling - simple role assignment on join"""
        try:
            guild_id = member.guild.id
            logger.info(f"👤 Member {member.display_name} ({member.id}) joined guild {guild_id}")

            # Skip bots immediately
            if member.bot:
                logger.debug(f"Skipping auto-roling for bot {member.display_name}")
                return

            # Check if auto-roling is enabled for this server
            if not self.is_feature_enabled_for_server(guild_id, 'auto_roling'):
                logger.debug(f"Auto-roling disabled for server {guild_id}")
                return

            # Get server configuration and ignored users
            config = self.db.get_server_config(guild_id)
            ignored_users = config.get('ignored_users', []) if config else []

            if member.id in ignored_users:
                logger.info(f"Skipping auto-roling for ignored user {member.display_name} ({member.id})")
                return

            # Get auto-roling settings
            auto_roling_settings = self.db.get_auto_roling_settings(guild_id)
            if not auto_roling_settings:
                logger.debug(f"No auto-roling settings found for guild {guild_id}")
                return

            if not auto_roling_settings.get('enabled', False):
                logger.debug(f"Auto-roling disabled in settings for guild {guild_id}")
                return

            if auto_roling_settings.get('permission_error', False):
                logger.warning(f"Auto-roling has permission error for guild {guild_id} - skipping")
                return

            logger.info(f"🎯 Processing auto-roling for {member.display_name} in {member.guild.name}")

            # Check premium status for advanced features
            has_premium = self.is_server_premium(guild_id)
            logger.info(f"Guild {guild_id} premium status: {has_premium}")

            # ALWAYS assign basic auto-role (FREE feature) - this is the main purpose
            await self.assign_basic_auto_role(member, auto_roling_settings)

            # Assign additional roles if premium (PREMIUM feature)
            if has_premium:
                await self.assign_additional_roles(member, auto_roling_settings)
            else:
                logger.debug(f"No premium access - skipping additional roles for {member.display_name}")

        except Exception as e:
            logger.error(f"Error in auto-roling on_member_join handler: {e}", exc_info=True)

    # ========== ROLE ASSIGNMENT METHODS ==========

    async def assign_basic_auto_role(self, member, auto_roling_settings):
        """Assign the basic auto-role (FREE feature)"""
        try:
            guild_id = member.guild.id
            role_id = auto_roling_settings.get('role_id')

            if not role_id:
                logger.debug(f"No basic role_id configured for guild {guild_id}")
                return

            role = member.guild.get_role(role_id)
            if not role:
                error_msg = f"Basic auto-role {role_id} not found"
                logger.error(f"{error_msg} in guild {guild_id}")
                self.db.set_auto_roling_error(guild_id, error_msg)
                return

            logger.info(f"Assigning basic auto-role {role.name} to {member.display_name}")
            await self.assign_role_with_checks(member, role, "basic auto-role")

        except Exception as e:
            logger.error(f"Error assigning basic auto-role: {e}", exc_info=True)

    async def assign_additional_roles(self, member, auto_roling_settings):
        """Assign additional roles (PREMIUM feature)"""
        try:
            guild_id = member.guild.id
            additional_roles = self.db.get_multiple_join_roles(guild_id)

            if not additional_roles:
                logger.debug(f"No additional roles configured for guild {guild_id}")
                return

            logger.info(f"Assigning {len(additional_roles)} additional roles to {member.display_name}")

            for role_id in additional_roles:
                if not role_id:
                    continue

                role = member.guild.get_role(role_id)
                if role:
                    await self.assign_role_with_checks(member, role, "additional auto-role")
                    await asyncio.sleep(0.1)  # Small delay to avoid rate limits
                else:
                    logger.warning(f"Additional auto-role {role_id} not found in guild {guild_id}")

        except Exception as e:
            logger.error(f"Error assigning additional roles: {e}", exc_info=True)

    async def assign_role_with_checks(self, member, role, role_type: str):
        """Assign a role with comprehensive permission and hierarchy checks"""
        try:
            guild_id = member.guild.id

            # Check if member already has the role
            if role in member.roles:
                logger.debug(f"Member {member.display_name} already has {role_type} {role.name}")
                return

            # Check bot permissions
            bot_member = member.guild.get_member(self.bot.user.id)
            if not bot_member:
                logger.error(f"Bot not found in guild {guild_id}")
                return

            if not bot_member.guild_permissions.manage_roles:
                error_msg = "Bot doesn't have 'Manage Roles' permission"
                logger.error(f"Permission error in guild {guild_id}: {error_msg}")
                self.db.set_auto_roling_error(guild_id, error_msg)
                return

            # Check role hierarchy
            if role.position >= bot_member.top_role.position:
                error_msg = f"Role '{role.name}' is higher than bot's highest role"
                logger.error(f"Hierarchy error in guild {guild_id}: {error_msg}")
                self.db.set_auto_roling_error(guild_id, error_msg)
                return

            # Rate limiting check
            rate_limit_key = f"{guild_id}_{member.id}"
            current_time = datetime.now(timezone.utc)

            if rate_limit_key in self.role_assignment_cooldown:
                last_assignment = self.role_assignment_cooldown[rate_limit_key]
                if (current_time - last_assignment).total_seconds() < 1:
                    logger.debug(f"Rate limiting role assignment for {member.display_name}")
                    await asyncio.sleep(1)

            # Assign the role
            await member.add_roles(role, reason=f"Auto-roling system: {role_type}")
            self.role_assignment_cooldown[rate_limit_key] = current_time

            logger.info(f"Successfully assigned {role_type} {role.name} to {member.display_name}")

            # Clear any previous permission errors (only for basic roles to avoid spam)
            if "basic" in role_type:
                self.db.clear_auto_roling_error(guild_id)

            # Log to database for dashboard
            self.db.log_bot_activity(
                guild_id,
                member.id,
                f"{member.name}#{member.discriminator}",
                f"Auto-role assigned: {role.name} ({role_type})",
                f"New member automatically received {role_type}",
                "auto_roling",
                None
            )

        except discord.Forbidden:
            error_msg = f"Bot doesn't have permission to assign role '{role.name}'"
            logger.error(f"Permission error in guild {guild_id}: {error_msg}")
            if "basic" in role_type:  # Only set error for basic roles
                self.db.set_auto_roling_error(guild_id, error_msg)
        except discord.HTTPException as e:
            error_msg = f"HTTP error assigning role '{role.name}': {e}"
            logger.error(f"HTTP error in guild {guild_id}: {error_msg}")
            if "basic" in role_type:  # Only set error for basic roles
                self.db.set_auto_roling_error(guild_id, error_msg)
        except Exception as e:
            error_msg = f"Unexpected error assigning role '{role.name}': {e}"
            logger.error(f"Unexpected error in guild {guild_id}: {error_msg}")
            if "basic" in role_type:  # Only set error for basic roles
                self.db.set_auto_roling_error(guild_id, error_msg)



    # ========== OFFLINE PROTECTION BACKGROUND TASK ==========

    @tasks.loop(minutes=2)  # Check every 2 minutes for offline protection (PREMIUM feature)
    async def offline_protection_task(self):
        """Background task to assign roles to members who joined while bot was offline (PREMIUM)"""
        if not self.bot.is_ready():
            return

        try:
            # First, check if ANY guild has offline protection enabled
            # If none do, skip the entire task
            guilds_with_offline_protection = 0

            for guild in self.bot.guilds:
                if (self.is_feature_enabled_for_server(guild.id, 'auto_roling') and
                    self.is_server_premium(guild.id) and
                    self.db.is_offline_protection_enabled(guild.id)):
                    guilds_with_offline_protection += 1

            if guilds_with_offline_protection == 0:
                logger.debug(f"🚫 No guilds have offline protection enabled - skipping entire task")
                return

            logger.info(f"🔄 Starting offline protection check for {guilds_with_offline_protection} guilds with offline protection enabled")
            processed_guilds = 0

            for guild in self.bot.guilds:
                # Check if auto-roling is enabled for this server
                auto_roling_enabled = self.is_feature_enabled_for_server(guild.id, 'auto_roling')
                if not auto_roling_enabled:
                    continue

                # Check if server has premium access (required for offline protection)
                has_premium = self.is_server_premium(guild.id)
                if not has_premium:
                    continue

                # CRITICAL: Check if offline protection is explicitly enabled
                offline_protection_enabled = self.db.is_offline_protection_enabled(guild.id)

                # Enhanced logging to debug the issue
                auto_roling_settings = self.db.get_auto_roling_settings(guild.id)
                logger.info(f"🔍 DEBUG: Guild {guild.name} ({guild.id}) - offline_protection_enabled: {offline_protection_enabled}")
                logger.info(f"🔍 DEBUG: Auto-roling settings: {auto_roling_settings}")

                if not offline_protection_enabled:
                    # This should be caught by the pre-check above, but double-check
                    logger.info(f"🚫 Offline protection NOT enabled for guild {guild.name} ({guild.id}) - skipping")
                    continue

                logger.info(f"✅ Processing offline protection for guild {guild.name} ({guild.id})")

                # Get auto-roling settings (we already fetched this above for debugging)
                if not auto_roling_settings or not auto_roling_settings.get('enabled'):
                    logger.debug(f"Auto-roling not enabled for guild {guild.name} ({guild.id}) - skipping offline protection")
                    continue

                # Skip if there's a permission error
                if auto_roling_settings.get('permission_error'):
                    logger.debug(f"Permission error exists for guild {guild.name} ({guild.id}) - skipping offline protection")
                    continue

                # ADDITIONAL SAFETY CHECK: Double-check offline protection is explicitly enabled
                if not auto_roling_settings.get('offline_protection_enabled', False):
                    logger.warning(f"🚫 SAFETY CHECK: Offline protection field not found or False in settings for guild {guild.name} ({guild.id}) - skipping")
                    continue

                logger.info(f"🔄 Running offline protection for guild {guild.name} ({guild.id})")

                # Get all roles that should be assigned automatically
                roles_to_assign = []

                # Basic role (always included if configured)
                basic_role_id = auto_roling_settings.get('role_id')
                if basic_role_id:
                    basic_role = guild.get_role(basic_role_id)
                    if basic_role:
                        roles_to_assign.append(('basic', basic_role))
                        logger.debug(f"Will check for basic role: {basic_role.name}")

                # Additional roles (premium only)
                additional_roles = self.db.get_multiple_join_roles(guild.id)
                for role_id in additional_roles:
                    if role_id:
                        role = guild.get_role(role_id)
                        if role:
                            roles_to_assign.append(('additional', role))
                            logger.debug(f"Will check for additional role: {role.name}")

                if not roles_to_assign:
                    logger.debug(f"No roles configured for offline protection in guild {guild.name}")
                    continue

                # Get ignored users list
                config = self.db.get_server_config(guild.id)
                ignored_users = config.get('ignored_users', []) if config else []

                # Find human members missing auto-roles
                members_to_process = []
                total_members = 0
                bot_members = 0
                ignored_members = 0

                for member in guild.members:
                    total_members += 1

                    # ALWAYS skip Discord bots
                    if member.bot:
                        bot_members += 1
                        continue

                    # Skip ignored users
                    if member.id in ignored_users:
                        ignored_members += 1
                        continue

                    # Check if this human member is missing any auto-roles
                    missing_roles = []
                    for role_type, role in roles_to_assign:
                        if role not in member.roles:
                            missing_roles.append((role_type, role))

                    if missing_roles:
                        members_to_process.append((member, missing_roles))

                logger.info(f"Guild {guild.name}: {total_members} total members, {bot_members} bots (skipped), {ignored_members} ignored, {len(members_to_process)} need role assignment")

                # Process members with rate limiting (max 5 per cycle to be conservative)
                processed_count = 0
                for member, missing_roles in members_to_process[:5]:
                    try:
                        logger.info(f"Offline protection: Assigning {len(missing_roles)} missing roles to {member.display_name}")

                        for role_type, role in missing_roles:
                            await self.assign_role_with_checks(member, role, f"offline protection ({role_type})")
                            await asyncio.sleep(0.5)  # Longer delay between role assignments

                        processed_count += 1
                        if processed_count >= 5:  # Conservative safety limit
                            break

                    except Exception as e:
                        logger.error(f"Error in offline protection for {member.display_name}: {e}")

                if processed_count > 0:
                    logger.info(f"✅ Offline protection: processed {processed_count} members in guild {guild.name}")

                processed_guilds += 1

            if processed_guilds > 0:
                logger.info(f"Offline protection cycle completed: processed {processed_guilds} guilds")

        except Exception as e:
            logger.error(f"Error in offline protection task: {e}", exc_info=True)

    @offline_protection_task.before_loop
    async def before_offline_protection_task(self):
        """Wait for the bot to be ready before starting the offline protection task"""
        await self.bot.wait_until_ready()
        logger.info("🚀 Offline protection background task started")

    @commands.command(name='stop-offline-protection')
    @commands.has_permissions(administrator=True)
    async def stop_offline_protection_task(self, ctx):
        """Stop the offline protection background task (Admin only)"""
        try:
            if self.offline_protection_task.is_running():
                self.offline_protection_task.cancel()
                await ctx.send("🛑 Offline protection background task stopped.")
                logger.info("Offline protection task manually stopped")
            else:
                await ctx.send("ℹ️ Offline protection task is not running.")
        except Exception as e:
            await ctx.send(f"❌ Error stopping offline protection task: {e}")

    @commands.command(name='start-offline-protection')
    @commands.has_permissions(administrator=True)
    async def start_offline_protection_task(self, ctx):
        """Start the offline protection background task (Admin only)"""
        try:
            if not self.offline_protection_task.is_running():
                self.offline_protection_task.start()
                await ctx.send("▶️ Offline protection background task started.")
                logger.info("Offline protection task manually started")
            else:
                await ctx.send("ℹ️ Offline protection task is already running.")
        except Exception as e:
            await ctx.send(f"❌ Error starting offline protection task: {e}")

    @commands.command(name='offline-protection-status')
    @commands.has_permissions(administrator=True)
    async def offline_protection_task_status(self, ctx):
        """Check if the offline protection background task is running (Admin only)"""
        try:
            is_running = self.offline_protection_task.is_running()

            # Get current server's offline protection settings
            auto_roling_settings = self.db.get_auto_roling_settings(ctx.guild.id)
            offline_enabled = self.db.is_offline_protection_enabled(ctx.guild.id)
            has_premium = self.is_server_premium(ctx.guild.id)

            embed = discord.Embed(
                title="🛡️ Offline Protection Status",
                color=0x00ff00 if (is_running and offline_enabled) else 0xff0000
            )

            embed.add_field(
                name="Background Task",
                value="✅ Running" if is_running else "❌ Stopped",
                inline=True
            )

            embed.add_field(
                name="Server Premium",
                value="✅ Yes" if has_premium else "❌ No",
                inline=True
            )

            embed.add_field(
                name="Offline Protection",
                value="✅ Enabled" if offline_enabled else "❌ Disabled",
                inline=True
            )

            if is_running:
                embed.add_field(
                    name="Next Run",
                    value=f"<t:{int(self.offline_protection_task.next_iteration.timestamp())}:R>",
                    inline=False
                )

            # Show raw settings for debugging
            embed.add_field(
                name="Debug Info",
                value=f"```json\n{auto_roling_settings}\n```"[:1024] if auto_roling_settings else "No settings found",
                inline=False
            )

            await ctx.send(embed=embed)

        except Exception as e:
            await ctx.send(f"❌ Error checking offline protection status: {e}")

    @commands.command(name='disable-offline-protection')
    @commands.has_permissions(administrator=True)
    async def disable_offline_protection(self, ctx):
        """Manually disable offline protection for this server (Admin only)"""
        try:
            # Disable offline protection in database
            success = self.db.save_offline_protection_settings(ctx.guild.id, False)

            if success:
                await ctx.send("✅ Offline protection has been disabled for this server.")
                logger.info(f"Offline protection manually disabled for guild {ctx.guild.id} by {ctx.author}")
            else:
                await ctx.send("❌ Failed to disable offline protection. Check logs for details.")

        except Exception as e:
            await ctx.send(f"❌ Error disabling offline protection: {e}")
            logger.error(f"Error disabling offline protection for guild {ctx.guild.id}: {e}")

    @commands.command(name='enable-offline-protection')
    @commands.has_permissions(administrator=True)
    async def enable_offline_protection(self, ctx):
        """Manually enable offline protection for this server (Admin only - requires premium)"""
        try:
            # Check if server has premium
            if not self.is_server_premium(ctx.guild.id):
                await ctx.send("❌ Offline protection requires premium access.")
                return

            # Enable offline protection in database
            success = self.db.save_offline_protection_settings(ctx.guild.id, True)

            if success:
                await ctx.send("✅ Offline protection has been enabled for this server.")
                logger.info(f"Offline protection manually enabled for guild {ctx.guild.id} by {ctx.author}")
            else:
                await ctx.send("❌ Failed to enable offline protection. Check logs for details.")

        except Exception as e:
            await ctx.send(f"❌ Error enabling offline protection: {e}")
            logger.error(f"Error enabling offline protection for guild {ctx.guild.id}: {e}")

    # ========== REACTION ROLES FUNCTIONALITY ==========

    @commands.Cog.listener()
    async def on_raw_reaction_add(self, payload):
        """Handle reaction additions for reaction roles (PREMIUM feature)"""
        try:
            # Skip bot reactions
            if payload.user_id == self.bot.user.id:
                return

            # Get reaction role configuration
            reaction_role_config = self.db.get_reaction_role_by_message(payload.message_id)
            if not reaction_role_config:
                return

            guild_id = int(reaction_role_config['server_id'])

            # Check if server has premium access
            if not self.is_server_premium(guild_id):
                logger.debug(f"Server {guild_id} doesn't have premium, skipping reaction role")
                return

            # Check if auto-roling is enabled
            if not self.is_feature_enabled_for_server(guild_id, 'auto_roling'):
                return

            # Get guild and member
            guild = self.bot.get_guild(guild_id)
            if not guild:
                return

            member = guild.get_member(payload.user_id)
            if not member:
                return

            # Get role from emoji
            role_emoji_map = reaction_role_config.get('role_emoji_map', {})
            emoji_str = str(payload.emoji)

            role_id = role_emoji_map.get(emoji_str)
            if not role_id:
                return

            role = guild.get_role(int(role_id))
            if not role:
                logger.warning(f"Reaction role {role_id} not found in guild {guild_id}")
                return

            # Assign the role
            await self.assign_role_with_checks(member, role, "reaction role")

        except Exception as e:
            logger.error(f"Error handling reaction add for reaction roles: {e}", exc_info=True)

    @commands.Cog.listener()
    async def on_raw_reaction_remove(self, payload):
        """Handle reaction removals for reaction roles (PREMIUM feature)"""
        try:
            # Skip bot reactions
            if payload.user_id == self.bot.user.id:
                return

            # Get reaction role configuration
            reaction_role_config = self.db.get_reaction_role_by_message(payload.message_id)
            if not reaction_role_config:
                return

            guild_id = int(reaction_role_config['server_id'])

            # Check if server has premium access
            if not self.is_server_premium(guild_id):
                return

            # Check if auto-roling is enabled
            if not self.is_feature_enabled_for_server(guild_id, 'auto_roling'):
                return

            # Get guild and member
            guild = self.bot.get_guild(guild_id)
            if not guild:
                return

            member = guild.get_member(payload.user_id)
            if not member:
                return

            # Get role from emoji
            role_emoji_map = reaction_role_config.get('role_emoji_map', {})
            emoji_str = str(payload.emoji)

            role_id = role_emoji_map.get(emoji_str)
            if not role_id:
                return

            role = guild.get_role(int(role_id))
            if not role or role not in member.roles:
                return

            # Remove the role
            try:
                await member.remove_roles(role, reason="Reaction role: reaction removed")
                logger.info(f"Removed reaction role {role.name} from {member.display_name}")

                # Log to database
                self.db.log_bot_activity(
                    guild_id,
                    member.id,
                    f"{member.name}#{member.discriminator}",
                    f"Reaction role removed: {role.name}",
                    f"Role removed via reaction removal",
                    "auto_roling",
                    None
                )

            except discord.Forbidden:
                logger.error(f"No permission to remove role {role.name} from {member.display_name}")
            except Exception as e:
                logger.error(f"Error removing reaction role: {e}")

        except Exception as e:
            logger.error(f"Error handling reaction remove for reaction roles: {e}", exc_info=True)

    # ========== EMBED DROPDOWN ROLES FUNCTIONALITY ==========

    @commands.Cog.listener()
    async def on_interaction(self, interaction):
        """Handle dropdown interactions for embed dropdown roles (PREMIUM feature)"""
        try:
            # Only handle select menu interactions
            if interaction.type != discord.InteractionType.component:
                return

            if not hasattr(interaction, 'data') or interaction.data.get('component_type') != 3:  # 3 = Select Menu
                return

            # Get embed dropdown configuration
            embed_dropdown_config = self.db.get_embed_dropdown_by_message(interaction.message.id)
            if not embed_dropdown_config:
                return

            guild_id = int(embed_dropdown_config['server_id'])

            # Check if server has premium access
            if not self.is_server_premium(guild_id):
                await interaction.response.send_message("This server doesn't have premium access for embed dropdown roles.", ephemeral=True)
                return

            # Check if auto-roling is enabled
            if not self.is_feature_enabled_for_server(guild_id, 'auto_roling'):
                await interaction.response.send_message("Auto-roling is disabled for this server.", ephemeral=True)
                return

            # Get selected role
            selected_values = interaction.data.get('values', [])
            if not selected_values:
                return

            role_id = int(selected_values[0])
            role = interaction.guild.get_role(role_id)
            if not role:
                await interaction.response.send_message("Selected role not found.", ephemeral=True)
                return

            member = interaction.user

            # Toggle role (add if not present, remove if present)
            if role in member.roles:
                try:
                    await member.remove_roles(role, reason="Embed dropdown role: role removed")
                    await interaction.response.send_message(f"Removed role **{role.name}**", ephemeral=True)

                    # Log to database
                    self.db.log_bot_activity(
                        guild_id,
                        member.id,
                        f"{member.name}#{member.discriminator}",
                        f"Embed dropdown role removed: {role.name}",
                        f"Role removed via dropdown selection",
                        "auto_roling",
                        None
                    )

                except discord.Forbidden:
                    await interaction.response.send_message("I don't have permission to remove that role.", ephemeral=True)
                except Exception as e:
                    logger.error(f"Error removing embed dropdown role: {e}")
                    await interaction.response.send_message("An error occurred while removing the role.", ephemeral=True)
            else:
                try:
                    await self.assign_role_with_checks(member, role, "embed dropdown role")
                    await interaction.response.send_message(f"Added role **{role.name}**", ephemeral=True)
                except Exception as e:
                    logger.error(f"Error adding embed dropdown role: {e}")
                    await interaction.response.send_message("An error occurred while adding the role.", ephemeral=True)

        except Exception as e:
            logger.error(f"Error handling embed dropdown interaction: {e}", exc_info=True)
            try:
                await interaction.response.send_message("An error occurred while processing your selection.", ephemeral=True)
            except:
                pass


async def setup(bot):
    """Setup function for the cog"""
    await bot.add_cog(AutoRolingSystem(bot))
