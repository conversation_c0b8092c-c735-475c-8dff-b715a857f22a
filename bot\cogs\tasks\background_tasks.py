import discord
from discord.ext import commands, tasks
import logging
from datetime import datetime, timezone, timedelta
import asyncio

logger = logging.getLogger(__name__)

class BackgroundTasks(commands.Cog):
    """Handles background tasks for the bot"""
    
    def __init__(self, bot):
        self.bot = bot
        self.db = bot.db
    
    async def cog_load(self):
        """Start background tasks when cog loads"""
        if not self.check_giveaways.is_running():
            self.check_giveaways.start()
        
        if not self.cleanup_old_tickets.is_running():
            self.cleanup_old_tickets.start()
        
        if not self.check_auto_roling.is_running():
            self.check_auto_roling.start()
        
        if not self.cleanup_music_connections.is_running():
            self.cleanup_music_connections.start()
    
    async def cog_unload(self):
        """Stop background tasks when cog unloads"""
        if self.check_giveaways.is_running():
            self.check_giveaways.cancel()
        
        if self.cleanup_old_tickets.is_running():
            self.cleanup_old_tickets.cancel()
        
        if self.check_auto_roling.is_running():
            self.check_auto_roling.cancel()
        
        if self.cleanup_music_connections.is_running():
            self.cleanup_music_connections.cancel()

    @tasks.loop(seconds=30)
    async def check_giveaways(self):
        """Check for expired giveaways every 30 seconds"""
        try:
            expired_giveaways = self.db.get_expired_giveaways()

            for giveaway in expired_giveaways:
                try:
                    # Get the giveaways cog to handle ending
                    giveaways_cog = self.bot.get_cog('GiveawaySystem')
                    if giveaways_cog:
                        await giveaways_cog.end_giveaway(giveaway)
                    else:
                        logger.error("GiveawaySystem cog not found")
                except Exception as e:
                    logger.error(f"Error ending giveaway {giveaway['_id']}: {e}")

        except Exception as e:
            logger.error(f"Error in check_giveaways: {e}", exc_info=True)

    @tasks.loop(seconds=10)  # Check every 10 seconds for auto-roling offline protection
    async def check_auto_roling(self):
        """Check for members missing auto-roles (offline protection) - ONLY when offline protection is enabled"""
        if not self.bot.is_ready():
            return

        try:
            # Check all guilds the bot is in for auto-roling
            for guild in self.bot.guilds:
                guild_id = guild.id

                # Check if auto-roling is explicitly disabled (for premium enforcement)
                config = self.db.get_server_config(guild_id)
                if config and config.get('auto_roling_enabled') is False:
                    logger.debug(f"[BACKGROUND_TASKS] Auto-roling disabled for guild {guild.name} ({guild_id})")
                    continue

                # Check if auto-roling is enabled
                auto_role_config = self.db.get_auto_roling_settings(guild_id)
                if not auto_role_config or not auto_role_config.get('enabled'):
                    logger.debug(f"[BACKGROUND_TASKS] Auto-roling not enabled for guild {guild.name} ({guild_id})")
                    continue

                # CRITICAL: Check if offline protection is enabled (this is a PREMIUM feature)
                offline_protection_enabled = auto_role_config.get('offline_protection_enabled', False)
                if not offline_protection_enabled:
                    logger.debug(f"[BACKGROUND_TASKS] Offline protection disabled for guild {guild.name} ({guild_id}) - skipping")
                    continue

                # Check if server has premium access (required for offline protection)
                auto_roling_cog = self.bot.get_cog('AutoRolingSystem')
                if auto_roling_cog and not auto_roling_cog.is_server_premium(guild_id):
                    logger.debug(f"[BACKGROUND_TASKS] No premium access for guild {guild.name} ({guild_id}) - skipping offline protection")
                    continue

                logger.info(f"[BACKGROUND_TASKS] Processing offline protection for guild {guild.name} ({guild_id})")

                role_id = auto_role_config.get('role_id')
                if not role_id:
                    continue

                role = guild.get_role(role_id)
                if not role:
                    continue

                # Get ignored users list
                ignored_users = set(auto_role_config.get('ignored_users', []))

                # Find members without the auto-role (excluding ignored users)
                members_to_assign = []
                for member in guild.members:
                    if (member.id not in ignored_users and
                        role not in member.roles and
                        not member.bot):  # Skip bots for now, can be configured later
                        members_to_assign.append(member)

                logger.info(f"[BACKGROUND_TASKS] Found {len(members_to_assign)} members needing roles in {guild.name}")

                # Assign roles with rate limiting
                for i, member in enumerate(members_to_assign[:5]):  # Limit to 5 per check to avoid rate limits
                    try:
                        await self._assign_auto_role(member, role, guild.id)
                        # Small delay between assignments
                        if i < len(members_to_assign) - 1:
                            await asyncio.sleep(0.1)
                    except Exception as e:
                        logger.error(f"[BACKGROUND_TASKS] Error assigning auto-role to {member.display_name}: {e}")

        except Exception as e:
            logger.error(f"[BACKGROUND_TASKS] Error in check_auto_roling task: {e}")

    @tasks.loop(minutes=5)  # Check every 5 minutes for more responsive cleanup
    async def cleanup_old_tickets(self):
        """Clean up tickets older than 24 hours and handle channel deletions"""
        try:
            # Check if database manager is initialized
            if not hasattr(self.bot, 'db') or not hasattr(self.bot.db, 'db'):
                logger.error("Database manager not properly initialized")
                return

            # Check database connection
            try:
                # This will raise an exception if not connected
                self.bot.db.client.admin.command('ping')
            except Exception as e:
                logger.warning("Database connection lost, attempting to reconnect...")
                try:
                    self.bot.db.connect()
                    logger.info("Successfully reconnected to database")
                except Exception as connect_error:
                    logger.error(f"Failed to reconnect to database: {connect_error}")
                    return

            # Gender verification cleanup removed

            # Run cleanup for DM support tickets
            try:
                dm_count = await self._cleanup_dm_support_tickets()
                if dm_count > 0:
                    logger.info(f"Successfully cleaned up {dm_count} old DM support tickets")
            except Exception as e:
                logger.error(f"Error during DM support ticket cleanup: {e}", exc_info=True)

            # Check for deleted channels and auto-close tickets
            try:
                deleted_count = await self._cleanup_deleted_channel_tickets()
                if deleted_count > 0:
                    logger.info(f"Auto-closed {deleted_count} tickets due to deleted channels")
            except Exception as e:
                logger.error(f"Error during deleted channel cleanup: {e}", exc_info=True)

        except Exception as outer_e:
            logger.critical(f"Critical error in cleanup_old_tickets task: {outer_e}", exc_info=True)

    @tasks.loop(minutes=2)
    async def cleanup_music_connections(self):
        """Clean up disconnected music voice clients"""
        try:
            if hasattr(self.bot, 'music_manager'):
                await self.bot.music_manager.cleanup_disconnected_clients()
        except Exception as e:
            logger.error(f"Error in cleanup_music_connections task: {e}")

    # Helper methods
    async def _assign_auto_role(self, member, role, guild_id):
        """Assign auto-role to a member"""
        try:
            # Check if member still doesn't have the role (race condition protection)
            if role in member.roles:
                return

            # Add the role
            await member.add_roles(role, reason="Auto-roling system")

            logger.info(f"[BACKGROUND_TASKS] Assigned auto-role {role.name} to {member.display_name} in guild {member.guild.name}")

            # Log to database for dashboard
            try:
                self.db.log_bot_activity(
                    guild_id,
                    member.id,
                    f"{member.name}#{member.discriminator}",
                    f"Auto-role assigned: {role.name}",
                    f"New member automatically received role",
                    "auto_roling",
                    None
                )

                # Also log to Discord if enabled
                logging_cog = self.bot.get_cog('LoggingSystem')
                if logging_cog:
                    await logging_cog.log_bot_activity_to_channel(
                        guild_id, "ryzuo_logs", member.id, f"{member.name}#{member.discriminator}",
                        f"Auto-role assigned: {role.name}",
                        f"New member automatically received role",
                        None
                    )
            except Exception as e:
                logger.error(f"Failed to log auto-role assignment: {e}")

        except discord.Forbidden:
            logger.error(f"Missing permissions to assign role {role.name} to {member.display_name}")
        except discord.HTTPException as e:
            logger.error(f"HTTP error assigning role {role.name} to {member.display_name}: {e}")
        except Exception as e:
            logger.error(f"Unexpected error assigning role {role.name} to {member.display_name}: {e}")

    async def _cleanup_dm_support_tickets(self):
        """Clean up DM support tickets that are inactive for more than 24 hours"""
        try:
            collection = self.bot.db.db['ryzuo-dm-support-tickets']
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)

            # Find tickets that are open but haven't had activity in 24 hours
            old_tickets = list(collection.find({
                "status": "open",
                "created_at": {"$lt": cutoff_time}
            }))

            closed_count = 0
            for ticket in old_tickets:
                try:
                    # Check if there are recent messages (activity)
                    messages = ticket.get('messages', [])
                    if messages:
                        # Check last message timestamp
                        last_message_time = max(msg.get('timestamp', datetime.min.replace(tzinfo=timezone.utc)) for msg in messages)
                        if last_message_time > cutoff_time:
                            continue  # Skip if there's recent activity

                    # Close the ticket
                    collection.update_one(
                        {"_id": ticket["_id"]},
                        {"$set": {"status": "closed", "closed_at": datetime.now(timezone.utc), "closed_reason": "Inactive for 24+ hours"}}
                    )
                    closed_count += 1

                    # Try to notify the user
                    try:
                        user = self.bot.get_user(ticket['user_id'])
                        if user:
                            embed = discord.Embed(
                                title="🎫 Support Ticket Closed",
                                description="Your support ticket has been automatically closed due to inactivity.",
                                color=discord.Color.orange()
                            )
                            embed.add_field(name="Reason", value="No activity for 24+ hours", inline=False)
                            embed.add_field(name="Need help?", value="Feel free to send another message to create a new ticket.", inline=False)
                            await user.send(embed=embed)
                    except:
                        pass  # Ignore if we can't send DM

                except Exception as e:
                    logger.error(f"Error processing DM ticket {ticket.get('_id', 'unknown')}: {e}")

            return closed_count

        except Exception as e:
            logger.error(f"Error in cleanup_dm_support_tickets: {e}", exc_info=True)
            return 0

    async def _cleanup_deleted_channel_tickets(self):
        """Clean up tickets for channels that no longer exist"""
        try:
            closed_count = 0

            # Check DM support tickets
            dm_collection = self.bot.db.db['ryzuo-dm-support-tickets']
            dm_tickets = list(dm_collection.find({"status": "open"}))

            for ticket in dm_tickets:
                try:
                    channel_id = ticket.get('channel_id')
                    if not channel_id:
                        continue

                    # Check if channel still exists
                    channel = self.bot.get_channel(channel_id)
                    if not channel:
                        # Channel doesn't exist, close the ticket
                        dm_collection.update_one(
                            {"_id": ticket["_id"]},
                            {"$set": {"status": "closed", "closed_at": datetime.now(timezone.utc), "closed_reason": "Channel deleted"}}
                        )
                        closed_count += 1

                except Exception as e:
                    logger.error(f"Error processing DM support ticket {ticket.get('_id', 'unknown')}: {e}")

            return closed_count

        except Exception as e:
            logger.error(f"Error in cleanup_deleted_channel_tickets: {e}", exc_info=True)
            return 0

    # Wait for bot to be ready before starting tasks
    @check_giveaways.before_loop
    async def before_check_giveaways(self):
        await self.bot.wait_until_ready()

    @cleanup_old_tickets.before_loop
    async def before_cleanup_old_tickets(self):
        await self.bot.wait_until_ready()

    @check_auto_roling.before_loop
    async def before_check_auto_roling(self):
        await self.bot.wait_until_ready()

    @cleanup_music_connections.before_loop
    async def before_cleanup_music_connections(self):
        await self.bot.wait_until_ready()

async def setup(bot):
    await bot.add_cog(BackgroundTasks(bot))
