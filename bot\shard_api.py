"""
Shard API Service
Provides HTTP API endpoints for the website to communicate with this shard
"""

import asyncio
import logging
import os
import threading
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
import discord
from flask import Flask, request, jsonify
from functools import wraps

logger = logging.getLogger(__name__)

class ShardAPIService:
    def __init__(self, bot: discord.Client, shard_id: int):
        self.bot = bot
        self.shard_id = shard_id
        self.app = Flask(f'shard-{shard_id}-api')
        self.api_key = os.getenv('SHARD_API_KEY')
        self.port = int(os.getenv('SHARD_API_PORT'))
        self.host = os.getenv('SHARD_API_HOST')
        
        # Setup routes
        self._setup_routes()
        
        # API server thread
        self.server_thread = None
        
    def _setup_routes(self):
        """Setup API routes"""
        
        def require_api_key(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                auth_header = request.headers.get('Authorization')
                if not auth_header or not auth_header.startswith('Bearer '):
                    return jsonify({'error': 'Missing or invalid authorization header'}), 401
                
                token = auth_header.split(' ')[1]
                if token != self.api_key:
                    return jsonify({'error': 'Invalid API key'}), 401
                
                return f(*args, **kwargs)
            return decorated_function
        
        @self.app.route('/api/server/<server_id>')
        @require_api_key
        def get_server_info(server_id):
            """Get server information"""
            try:
                guild = self.bot.get_guild(int(server_id))
                if not guild:
                    return jsonify({'error': 'Server not found'}), 404
                
                # Get icon URL with fallback
                icon_url = None
                if guild.icon:
                    icon_url = str(guild.icon.url)
                else:
                    # Use Discord's default server icon based on server ID
                    icon_url = f"https://cdn.discordapp.com/embed/avatars/{guild.id % 5}.png"

                return jsonify({
                    'id': guild.id,
                    'name': guild.name,
                    'icon': icon_url,
                    'member_count': guild.member_count,
                    'owner_id': guild.owner_id,
                    'description': guild.description,
                    'created_at': guild.created_at.isoformat(),
                    'shard_id': self.shard_id
                })
            except Exception as e:
                logger.error(f"Error getting server info: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/server/<server_id>/channels')
        @require_api_key
        def get_server_channels(server_id):
            """Get server channels"""
            try:
                guild = self.bot.get_guild(int(server_id))
                if not guild:
                    return jsonify({'error': 'Server not found'}), 404
                
                channels = []
                for channel in guild.channels:
                    channel_info = {
                        'id': str(channel.id),
                        'name': channel.name,
                        'type': '',
                        'category': channel.category.name if channel.category else None,
                        'position': channel.position
                    }
                    
                    if isinstance(channel, discord.TextChannel):
                        channel_info['type'] = 'text'
                        channels.append(channel_info)
                    elif isinstance(channel, discord.VoiceChannel):
                        channel_info['type'] = 'voice'
                        channels.append(channel_info)
                    elif isinstance(channel, discord.CategoryChannel):
                        channel_info['type'] = 'category'
                        channel_info['category'] = None
                        channels.append(channel_info)
                    elif isinstance(channel, discord.ForumChannel):
                        channel_info['type'] = 'forum'
                        channels.append(channel_info)
                
                return jsonify({'channels': channels})
            except Exception as e:
                logger.error(f"Error getting server channels: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/server/<server_id>/members')
        @require_api_key
        def get_server_members(server_id):
            """Get server members"""
            try:
                guild = self.bot.get_guild(int(server_id))
                if not guild:
                    return jsonify({'error': 'Server not found'}), 404
                
                members = []
                for member in guild.members:
                    if member.bot:
                        continue
                    
                    avatar_url = str(member.avatar.url) if member.avatar else f'https://cdn.discordapp.com/embed/avatars/{int(member.discriminator) % 5}.png'
                    
                    members.append({
                        'id': str(member.id),
                        'name': f'{member.name}#{member.discriminator}',
                        'display_name': member.display_name,
                        'avatar_url': avatar_url,
                        'joined_at': member.joined_at.isoformat() if member.joined_at else None
                    })
                
                return jsonify({'members': members})
            except Exception as e:
                logger.error(f"Error getting server members: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/server/<server_id>/roles')
        @require_api_key
        def get_server_roles(server_id):
            """Get server roles"""
            try:
                guild = self.bot.get_guild(int(server_id))
                if not guild:
                    return jsonify({'error': 'Server not found'}), 404
                
                roles = []
                for role in guild.roles:
                    if role.name != '@everyone':  # Skip @everyone role
                        roles.append({
                            'id': str(role.id),
                            'name': role.name,
                            'color': role.color.value,
                            'position': role.position,
                            'mentionable': role.mentionable,
                            'managed': role.managed
                        })
                
                # Sort by position (highest first)
                roles.sort(key=lambda x: x['position'], reverse=True)
                return jsonify({'roles': roles})
            except Exception as e:
                logger.error(f"Error getting server roles: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/server/<server_id>/bot-status')
        @require_api_key
        def get_bot_status(server_id):
            """Check if bot is in server"""
            try:
                guild = self.bot.get_guild(int(server_id))
                return jsonify({
                    'bot_in_server': guild is not None,
                    'shard_id': self.shard_id
                })
            except Exception as e:
                logger.error(f"Error checking bot status: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/server/<server_id>/send-message', methods=['POST'])
        @require_api_key
        def send_message(server_id):
            """Send a message to a channel"""
            try:
                data = request.get_json()
                channel_id = data.get('channel_id')
                content = data.get('content')
                embed_data = data.get('embed')
                
                guild = self.bot.get_guild(int(server_id))
                if not guild:
                    return jsonify({'error': 'Server not found'}), 404
                
                channel = guild.get_channel(int(channel_id))
                if not channel:
                    return jsonify({'error': 'Channel not found'}), 404
                
                # Create embed if provided
                embed = None
                if embed_data:
                    embed = discord.Embed.from_dict(embed_data)
                
                # Send message asynchronously
                async def send_async():
                    message = await channel.send(content=content, embed=embed)
                    return {
                        'message_id': str(message.id),
                        'channel_id': str(message.channel.id),
                        'timestamp': message.created_at.isoformat()
                    }
                
                future = asyncio.run_coroutine_threadsafe(send_async(), self.bot.loop)
                result = future.result(timeout=10)
                
                return jsonify({'success': True, 'message': result})
            except Exception as e:
                logger.error(f"Error sending message: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/server/<server_id>/post-tempvoice-interface', methods=['POST'])
        @require_api_key
        def post_tempvoice_interface(server_id):
            """Post tempvoice interface to a channel"""
            try:
                data = request.get_json()
                interface_channel_id = data.get('interface_channel_id')
                creator_channel_id = data.get('creator_channel_id')

                guild = self.bot.get_guild(int(server_id))
                if not guild:
                    return jsonify({'error': 'Server not found'}), 404

                interface_channel = guild.get_channel(int(interface_channel_id))
                if not interface_channel:
                    return jsonify({'error': 'Interface channel not found'}), 404

                creator_channel = guild.get_channel(int(creator_channel_id))
                if not creator_channel:
                    return jsonify({'error': 'Creator channel not found'}), 404

                # Post interface asynchronously
                async def post_interface_async():
                    # Create the interface embed
                    embed = discord.Embed(
                        title="🎤 Temporary Voice Channels",
                        description=f"Join {creator_channel.mention} to automatically create your temporary voice channel!",
                        color=discord.Color.blue()
                    )
                    embed.add_field(
                        name="How it works:",
                        value="• Join the creator channel\n• Your temp channel is created automatically\n• You get full control over your channel\n• Channel deletes when empty",
                        inline=False
                    )
                    embed.add_field(
                        name="Channel Controls:",
                        value="• Set user limits\n• Kick/block users\n• Lock/unlock channel\n• Transfer ownership\n• Rename channel",
                        inline=False
                    )
                    embed.set_footer(text="Powered by ryzuo Bot")

                    # Import TempVoiceView from main module
                    from main import TempVoiceView

                    # Create view with button
                    view = TempVoiceView(self.bot.db)

                    message = await interface_channel.send(embed=embed, view=view)
                    return {
                        'message_id': str(message.id),
                        'channel_id': str(message.channel.id),
                        'timestamp': message.created_at.isoformat()
                    }

                future = asyncio.run_coroutine_threadsafe(post_interface_async(), self.bot.loop)
                result = future.result(timeout=10)

                return jsonify({'success': True, 'interface': result})
            except Exception as e:
                logger.error(f"Error posting tempvoice interface: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/server/<server_id>/create-giveaway', methods=['POST'])
        @require_api_key
        def create_giveaway(server_id):
            """Create a giveaway message"""
            try:
                data = request.get_json()
                channel_id = data.get('channel_id')
                giveaway_data = data.get('giveaway_data')
                
                guild = self.bot.get_guild(int(server_id))
                if not guild:
                    return jsonify({'error': 'Server not found'}), 404
                
                channel = guild.get_channel(int(channel_id))
                if not channel:
                    return jsonify({'error': 'Channel not found'}), 404
                
                # Create giveaway message asynchronously
                async def create_giveaway_async():
                    # Import giveaway functions from main
                    from main import create_giveaway_embed, PersistentGiveawayView
                    from datetime import datetime

                    # Convert end_time from ISO string back to datetime object
                    if isinstance(giveaway_data.get('end_time'), str):
                        giveaway_data['end_time'] = datetime.fromisoformat(giveaway_data['end_time'])

                    embed = create_giveaway_embed(giveaway_data)
                    view = PersistentGiveawayView()

                    message = await channel.send(embed=embed, view=view)
                    return {
                        'message_id': str(message.id),
                        'channel_id': str(message.channel.id),
                        'timestamp': message.created_at.isoformat()
                    }
                
                future = asyncio.run_coroutine_threadsafe(create_giveaway_async(), self.bot.loop)
                result = future.result(timeout=10)
                
                return jsonify({'success': True, 'message': result})
            except Exception as e:
                logger.error(f"Error creating giveaway: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/server/<server_id>/reroll-giveaway', methods=['POST'])
        @require_api_key
        def reroll_giveaway(server_id):
            """Reroll a giveaway"""
            try:
                data = request.get_json()
                giveaway_id = data.get('giveaway_id')

                if not giveaway_id:
                    return jsonify({'error': 'Giveaway ID is required'}), 400

                guild = self.bot.get_guild(int(server_id))
                if not guild:
                    return jsonify({'error': 'Server not found'}), 404

                # Get giveaway from database
                giveaway = self.bot.db.get_giveaway(giveaway_id)
                if not giveaway:
                    return jsonify({'error': 'Giveaway not found'}), 404

                # Verify giveaway belongs to this server
                if giveaway.get('server_id') != int(server_id):
                    return jsonify({'error': 'Giveaway not found in this server'}), 404

                # Check if giveaway has ended
                if not giveaway.get('ended', False):
                    return jsonify({'error': 'Can only reroll ended giveaways'}), 400

                # Check if there are entries to reroll from
                entries = giveaway.get('entries', [])
                if not entries:
                    return jsonify({'error': 'No entries to reroll from'}), 400

                # Reroll giveaway asynchronously
                async def reroll_giveaway_async():
                    await self.bot.reroll_giveaway_winners_method(giveaway)
                    return {'success': True, 'message': 'Giveaway rerolled successfully'}

                future = asyncio.run_coroutine_threadsafe(reroll_giveaway_async(), self.bot.loop)
                result = future.result(timeout=10)

                return jsonify(result)
            except Exception as e:
                logger.error(f"Error rerolling giveaway: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/server/<server_id>/delete-giveaway-message', methods=['POST'])
        @require_api_key
        def delete_giveaway_message(server_id):
            """Delete a giveaway message"""
            try:
                data = request.get_json()
                channel_id = data.get('channel_id')
                message_id = data.get('message_id')

                if not channel_id or not message_id:
                    return jsonify({'error': 'Channel ID and message ID are required'}), 400

                guild = self.bot.get_guild(int(server_id))
                if not guild:
                    return jsonify({'error': 'Server not found'}), 404

                channel = guild.get_channel(int(channel_id))
                if not channel:
                    return jsonify({'error': 'Channel not found'}), 404

                # Delete message asynchronously
                async def delete_message_async():
                    try:
                        message = await channel.fetch_message(int(message_id))
                        await message.delete()
                        return {'success': True, 'message': 'Giveaway message deleted successfully'}
                    except discord.NotFound:
                        return {'success': True, 'message': 'Message was already deleted'}
                    except discord.Forbidden:
                        return {'error': 'Bot does not have permission to delete this message'}
                    except Exception as e:
                        logger.error(f"Error deleting message {message_id}: {e}")
                        return {'error': f'Failed to delete message: {str(e)}'}

                future = asyncio.run_coroutine_threadsafe(delete_message_async(), self.bot.loop)
                result = future.result(timeout=10)

                return jsonify(result)
            except Exception as e:
                logger.error(f"Error deleting giveaway message: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/server/<server_id>/end-giveaway-early', methods=['POST'])
        @require_api_key
        def end_giveaway_early(server_id):
            """End a giveaway early"""
            try:
                data = request.get_json()
                giveaway_id = data.get('giveaway_id')

                if not giveaway_id:
                    return jsonify({'error': 'Giveaway ID is required'}), 400

                guild = self.bot.get_guild(int(server_id))
                if not guild:
                    return jsonify({'error': 'Server not found'}), 404

                # Get giveaway from database
                giveaway = self.bot.db.get_giveaway(giveaway_id)
                if not giveaway:
                    return jsonify({'error': 'Giveaway not found'}), 404

                # Verify giveaway belongs to this server
                if giveaway.get('server_id') != int(server_id):
                    return jsonify({'error': 'Giveaway not found in this server'}), 404

                # Check if giveaway is active
                if not giveaway.get('active', False) or giveaway.get('ended', False):
                    return jsonify({'error': 'Can only end active giveaways early'}), 400

                # End giveaway early asynchronously
                async def end_giveaway_early_async():
                    # Import the end_giveaway function from main
                    from main import end_giveaway
                    await end_giveaway(giveaway)
                    return {'success': True, 'message': 'Giveaway ended early successfully'}

                future = asyncio.run_coroutine_threadsafe(end_giveaway_early_async(), self.bot.loop)
                result = future.result(timeout=10)

                return jsonify(result)
            except Exception as e:
                logger.error(f"Error ending giveaway early: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/server/<server_id>/statistics')
        @require_api_key
        def get_server_statistics(server_id):
            """Get server statistics"""
            try:
                guild = self.bot.get_guild(int(server_id))
                if not guild:
                    return jsonify({'error': 'Server not found'}), 404

                # Get basic statistics
                stats = {
                    'member_count': guild.member_count,
                    'channel_count': len(guild.channels),
                    'role_count': len(guild.roles),
                    'emoji_count': len(guild.emojis),
                    'boost_count': guild.premium_subscription_count,
                    'boost_level': guild.premium_tier,
                    'created_at': guild.created_at.isoformat(),
                    'shard_id': self.shard_id
                }

                return jsonify(stats)
            except Exception as e:
                logger.error(f"Error getting server statistics: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/statistics')
        @require_api_key
        def get_shard_statistics():
            """Get global statistics for this shard"""
            try:
                if not self.bot.is_ready():
                    return jsonify({'error': 'Bot not ready'}), 503

                # Calculate shard-wide statistics
                total_members = sum(guild.member_count or 0 for guild in self.bot.guilds)
                total_channels = sum(len(guild.channels) for guild in self.bot.guilds)
                total_roles = sum(len(guild.roles) for guild in self.bot.guilds)
                total_emojis = sum(len(guild.emojis) for guild in self.bot.guilds)

                stats = {
                    'shard_id': self.shard_id,
                    'guild_count': len(self.bot.guilds),
                    'member_count': total_members,
                    'channel_count': total_channels,
                    'role_count': total_roles,
                    'emoji_count': total_emojis,
                    'latency': round(self.bot.latency * 1000) if hasattr(self.bot, 'latency') else 0,
                    'uptime': int(time.time() - self.bot.start_time) if hasattr(self.bot, 'start_time') else 0,
                    'status': 'operational' if self.bot.is_ready() else 'connecting',
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }

                return jsonify(stats)
            except Exception as e:
                logger.error(f"Error getting shard statistics: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/servers')
        @require_api_key
        def get_all_servers():
            """Get list of all servers in this shard"""
            try:
                if not self.bot.is_ready():
                    return jsonify({'error': 'Bot not ready'}), 503

                servers = []
                for guild in self.bot.guilds:
                    try:
                        # Get basic server information
                        icon_url = None
                        if guild.icon:
                            icon_url = str(guild.icon.url)
                        else:
                            # Use Discord's default server icon based on server ID
                            icon_url = f"https://cdn.discordapp.com/embed/avatars/{guild.id % 5}.png"

                        server_data = {
                            'id': str(guild.id),
                            'name': guild.name,
                            'member_count': guild.member_count or 0,
                            'icon': icon_url,
                            'icon_hash': str(guild.icon) if guild.icon else None,
                            'owner_id': guild.owner_id,
                            'description': guild.description,
                            'created_at': guild.created_at.isoformat(),
                            'shard_id': self.shard_id
                        }
                        servers.append(server_data)
                    except Exception as e:
                        logger.error(f"Error processing guild {guild.id}: {e}")
                        continue

                # Sort by member count (descending)
                servers.sort(key=lambda x: x['member_count'], reverse=True)

                return jsonify({
                    'servers': servers,
                    'total_servers': len(servers),
                    'shard_id': self.shard_id
                })
            except Exception as e:
                logger.error(f"Error getting all servers: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/server/<server_id>/create-reaction-role', methods=['POST'])
        @require_api_key
        def create_reaction_role_message(server_id):
            """Create a reaction role message"""
            try:
                data = request.get_json()
                channel_id = data.get('channel_id')
                title = data.get('title', 'React to get roles!')
                description = data.get('description', 'React with the emojis below to get your roles!')
                role_emoji_pairs = data.get('role_emoji_pairs', [])

                if not channel_id:
                    return jsonify({'error': 'Channel ID is required'}), 400

                if not role_emoji_pairs:
                    return jsonify({'error': 'At least one role-emoji pair is required'}), 400

                guild = self.bot.get_guild(int(server_id))
                if not guild:
                    return jsonify({'error': 'Server not found'}), 404

                channel = guild.get_channel(int(channel_id))
                if not channel:
                    return jsonify({'error': 'Channel not found'}), 404

                # Create reaction role message asynchronously
                async def create_reaction_role_async():
                    try:
                        # Create embed
                        embed = discord.Embed(
                            title=title,
                            description=description,
                            color=0x3b82f6
                        )

                        # Add role-emoji pairs to embed
                        role_text = ""
                        for pair in role_emoji_pairs:
                            role_id = pair.get('role_id')
                            emoji = pair.get('emoji')

                            if role_id and emoji:
                                role = guild.get_role(int(role_id))
                                if role:
                                    role_text += f"{emoji} - {role.mention}\n"

                        if role_text:
                            embed.add_field(name="Roles", value=role_text, inline=False)

                        # Send message
                        message = await channel.send(embed=embed)

                        # Add reactions
                        for pair in role_emoji_pairs:
                            emoji = pair.get('emoji')
                            if emoji:
                                try:
                                    await message.add_reaction(emoji)
                                except discord.HTTPException:
                                    logger.warning(f"Failed to add reaction {emoji} to message {message.id}")

                        # Save to database - convert role_emoji_pairs to role_emoji_map
                        role_emoji_map = {}
                        for pair in role_emoji_pairs:
                            emoji = pair.get('emoji')
                            role_id = pair.get('role_id')
                            if emoji and role_id:
                                role_emoji_map[emoji] = int(role_id)

                        success = self.bot.db.save_reaction_role_message(
                            int(server_id),
                            message.id,
                            int(channel_id),
                            role_emoji_map
                        )

                        if success:
                            return {
                                'success': True,
                                'message': 'Reaction role message created successfully',
                                'message_id': message.id
                            }
                        else:
                            return {'error': 'Failed to save reaction role data to database'}

                    except discord.Forbidden:
                        return {'error': 'Bot does not have permission to send messages in this channel'}
                    except Exception as e:
                        logger.error(f"Error creating reaction role message: {e}")
                        return {'error': f'Failed to create reaction role message: {str(e)}'}

                future = asyncio.run_coroutine_threadsafe(create_reaction_role_async(), self.bot.loop)
                result = future.result(timeout=30)

                if 'error' in result:
                    return jsonify(result), 500
                else:
                    return jsonify(result)

            except Exception as e:
                logger.error(f"Error creating reaction role message: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/server/<server_id>/delete-reaction-role', methods=['POST'])
        @require_api_key
        def delete_reaction_role_message(server_id):
            """Delete a reaction role message"""
            try:
                data = request.get_json()
                message_id = data.get('message_id')

                if not message_id:
                    return jsonify({'error': 'Message ID is required'}), 400

                guild = self.bot.get_guild(int(server_id))
                if not guild:
                    return jsonify({'error': 'Server not found'}), 404

                # Delete reaction role message asynchronously
                async def delete_reaction_role_async():
                    try:
                        # Get reaction role data from database first
                        reaction_role = self.bot.db.get_reaction_role_by_message(int(message_id))
                        if not reaction_role:
                            return {'error': 'Reaction role message not found in database'}

                        # Verify it belongs to this server
                        if reaction_role.get('server_id') != int(server_id):
                            return {'error': 'Reaction role message not found in this server'}

                        channel_id = reaction_role.get('channel_id')
                        if channel_id:
                            channel = guild.get_channel(int(channel_id))
                            if channel:
                                try:
                                    message = await channel.fetch_message(int(message_id))
                                    await message.delete()
                                except discord.NotFound:
                                    # Message already deleted, that's fine
                                    pass
                                except discord.Forbidden:
                                    return {'error': 'Bot does not have permission to delete this message'}

                        # Remove from database
                        success = self.bot.db.remove_reaction_role_message(int(server_id), int(message_id))

                        if success:
                            return {
                                'success': True,
                                'message': 'Reaction role message deleted successfully'
                            }
                        else:
                            return {'error': 'Failed to delete reaction role data from database'}

                    except Exception as e:
                        logger.error(f"Error deleting reaction role message: {e}")
                        return {'error': f'Failed to delete reaction role message: {str(e)}'}

                future = asyncio.run_coroutine_threadsafe(delete_reaction_role_async(), self.bot.loop)
                result = future.result(timeout=30)

                if 'error' in result:
                    return jsonify(result), 500
                else:
                    return jsonify(result)

            except Exception as e:
                logger.error(f"Error deleting reaction role message: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/health')
        def health_check():
            """Health check endpoint"""
            return jsonify({
                'status': 'healthy',
                'shard_id': self.shard_id,
                'bot_ready': self.bot.is_ready(),
                'timestamp': datetime.now(timezone.utc).isoformat()
            })
    
    def start_server(self):
        """Start the API server in a separate thread"""
        if self.server_thread and self.server_thread.is_alive():
            logger.warning(f"API server for shard {self.shard_id} is already running")
            return
        
        def run_server():
            try:
                logger.info(f"Starting shard {self.shard_id} API server on {self.host}:{self.port}")
                self.app.run(host=self.host, port=self.port, debug=False, use_reloader=False, threaded=True)
            except Exception as e:
                logger.error(f"Error running shard API server: {e}")
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        logger.info(f"Shard {self.shard_id} API server thread started")
    
    def stop_server(self):
        """Stop the API server"""
        # Flask doesn't have a built-in way to stop the server gracefully
        # In production, you'd use a proper WSGI server like Gunicorn
        logger.info(f"Shard {self.shard_id} API server stop requested")
